"use client";

import { useFormContext } from "react-hook-form";
import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { Body3 } from "@/app/components/app-typography";
import { FORM_STYLES } from "./constants";

interface EventParameterItemProps {
  index: number;
  fieldId: string;
  onRemove: (index: number) => void;
}

export function EventParameterItem({ index, fieldId, onRemove }: EventParameterItemProps) {
  const { register } = useFormContext();

  return (
    <div
      key={fieldId}
      className="relative mb-[16px] rounded-lg border border-stroke-neutral-decorative p-6"
    >
      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Parameter Type"
            placeholder="e.g. address, uint256"
            {...register(`parameters.${index}.type`, {
              required: true,
            })}
            type="text"
          />
        </div>
        <div className={FORM_STYLES.fieldContainer}>
          <Body3
            as="div"
            className="flex items-center gap-2 pt-6"
            color="secondary"
          >
            <input
              type="checkbox"
              {...register(`parameters.${index}.isIndexed`)}
              className="size-4"
            />
            Indexed
          </Body3>
        </div>
      </div>

      <div className={FORM_STYLES.inputGroupSingle}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Replacement"
            {...register(`parameters.${index}.replacement`)}
            type="text"
            placeholder='bytes constant PAYLOAD = bytes(hex"XX")'
          />
        </div>
      </div>

      <div className="mb-4 flex items-center gap-2">
        <input
          type="checkbox"
          {...register(`parameters.${index}.unused`)}
          className="size-4"
        />
        <Body3 color="secondary">Unused</Body3>
      </div>

      <Body3 color="secondary" className="text-sm">
        Ex:{" "}
        <code className="rounded bg-back-neutral-tertiary px-1">
          uint8 DECIMALS = uint8(XX)
        </code>
        <br />
        Define the replacement in your contract. Note that you need to input XX to allow us to replace XX with the value from the event
      </Body3>

      <AppButton
        type="button"
        onClick={() => onRemove(index)}
        className="absolute right-2 top-2 text-status-error"
        variant="secondary"
        size="sm"
      >
        Remove
      </AppButton>
    </div>
  );
}
