"use client";

import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { AppInput } from "@/app/components/app-input";
import { AppInputDropdown } from "@/app/components/app-input-dropdown";
import { H1, H2, Body3 } from "@/app/components/app-typography";
import { useGetGovernanceFuzzing } from "@/app/services/governanceFuzzing.hook";
import { useGetRecipes } from "@/app/services/recipes.hook";
import { chains } from "@/lib/utils";
import axios from "axios";
import { ethers } from "ethers";
import { useEffect, useState } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";

// Form styling constants following create job form patterns
const FORM_STYLES = {
  // Responsive grid layout: single column on mobile, 2 columns on larger screens
  inputGroup: "mb-6 grid grid-cols-1 gap-6 xl:grid-cols-2",
  // Single input with min-width constraint
  input: "mb-[8px] min-w-[200px]",
  // Full width single column for special cases
  inputGroupSingle: "mb-6 grid grid-cols-1 gap-6",
  // Form container with responsive padding
  formContainer: "w-full space-y-6",
  // Field container with proper spacing
  fieldContainer: "w-full min-w-[200px]",
  divider: "h-px w-full bg-back-neutral-tertiary",
  sectionTitle: "text-xl font-bold leading-[1.3] text-fore-neutral-primary",
};

export type GitHubLinkFormValues = {
  contractAddress: string;
  chainId: number;
  recipeId: string;
  eventName: string;
  parameters: Array<{
    type: string;
    isIndexed: boolean;
    replacement?: string;
    unused: boolean;
  }>;
};

// ADD check for for fork replay ? (vm warp and roll )

export default function GovernanceFuzzing() {
  const methods = useForm<GitHubLinkFormValues>({
    defaultValues: {
      contractAddress: "",
      chainId: 1,
      recipeId: "",
      eventName: "",
      parameters: [],
    },
  });

  const { register, setValue, handleSubmit, watch, formState } = methods;
  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: "parameters",
  });
  const { data: recipes, isLoading } = useGetRecipes();
  const { data: governanceFuzzing } = useGetGovernanceFuzzing();
  const [showEventDefinition, setShowEventDefinition] = useState(false);
  const [topic, setTopic] = useState<string>("");
  const [eventDefinition, setEventDefinition] = useState<string>("");
  const [isEditing, setIsEditing] = useState<Map<string, boolean>>(new Map());
  const eventName = watch("eventName");
  const parameters = watch("parameters");
  const [editForm, setEditForm] = useState<{
    address: string;
    chainId: string;
    eventDefinition: string;
    topic: string;
    prepareContracts: Array<{ target: string; replacement: string }>;
    id: string;
  } | null>(null);

  useEffect(() => {
    if (eventName && parameters?.length > 0) {
      const eventDefConstructedForTopic = `${eventName}(${parameters
        .map((param) =>
          param.isIndexed
            ? `${param.type.trim().toLowerCase()}`
            : param.type.trim().toLowerCase()
        )
        .join(",")})`;
      setTopic(
        ethers.keccak256(ethers.toUtf8Bytes(eventDefConstructedForTopic))
      );
      const eventDefConstructed = `${eventName}(${parameters
        .map((param) =>
          param.isIndexed
            ? `${param.type.trim().toLowerCase()} indexed`
            : param.type.trim().toLowerCase()
        )
        .join(",")})`;
      setEventDefinition(eventDefConstructed);
    }
  }, [eventName, parameters, formState]);

  const onSubmit = async (data: GitHubLinkFormValues) => {
    // Construct event definition
    const eventDefConstructedForTopic = `${data.eventName}(${data.parameters
      .map((param) =>
        param.isIndexed
          ? `${param.type.trim().toLowerCase()}`
          : param.type.trim().toLowerCase()
      )
      .join(",")})`;

    const formattedEventToTopics = ethers.keccak256(
      ethers.toUtf8Bytes(eventDefConstructedForTopic)
    );

    const eventDefConstructed = `${data.eventName}(${data.parameters
      .map((param) =>
        param.isIndexed
          ? `${param.type.trim().toLowerCase()} indexed`
          : param.type.trim().toLowerCase()
      )
      .join(",")})`;
    if (
      !data.parameters.some((param) => param.replacement?.includes("XX")) &&
      data.parameters.some((param) => param.unused === false)
    ) {
      toast.error("No XX found in replacement");
      return;
    }
    const prepContract = data.parameters.map((param, index) => {
      // Split the string by XX and join all but the last part

      const parts = param.replacement.split("XX");
      let lastPart = parts.pop(); // Remove and get the last part
      if (lastPart.endsWith(";")) {
        lastPart = lastPart.slice(0, -1);
      }
      const newReplacement = parts.join("XX") + `$_${index + 1}` + lastPart;

      return {
        target: `${param.replacement}`,
        replacement: `${newReplacement};`,
        endOfTargetMarker: "[^;]*",
        targetContract: "Setup.sol",
        unused: param.unused,
      };
    });

    console.log({
      contractAddress: data.contractAddress,
      recipeId: data.recipeId,
      topic: formattedEventToTopics,
      eventDefinition: eventDefConstructed,
      prepContract,
      chainId: data.chainId,
    });

    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing`,
      data: {
        contractAddress: data.contractAddress,
        recipeId: data.recipeId,
        topic: formattedEventToTopics,
        eventDefinition: eventDefConstructed,
        prepContract: prepContract,
        chainId: data.chainId,
      },
    });
    if (response.status === 200) {
      toast.success("Governance fuzzing setup");
    } else {
      toast.error("Fail to create Governance Fuzzing");
    }
  };

  const handleDelete = async (id: string) => {
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/delete`,
      data: {
        id: id,
      },
    });
    if (response.status === 200) {
      // reload the page
      window.location.reload();
    }
  };

  const handleToggle = async (id: string) => {
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/toggle`,
      data: {
        id: id,
      },
    });
    if (response.status === 200) {
      window.location.reload();
    }
  };
  const contractAddress = watch("contractAddress");
  const chainId = watch("chainId");
  const recipeId = watch("recipeId");

  const handleEdit = async (id: string) => {
    setIsEditing(new Map(isEditing).set(id, true));
    const govFuzz = governanceFuzzing.find((g) => g.id === id);
    console.log(govFuzz);
    if (govFuzz) {
      setEditForm({
        address: govFuzz.address,
        chainId: govFuzz.chainId.toString(),
        eventDefinition: govFuzz.eventDefinition,
        topic: govFuzz.topic,
        prepareContracts:
          govFuzz.recipes[0]?.fuzzerArgs?.prepareContracts || [],
        id: govFuzz.id,
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setEditForm((prev) => {
      if (!prev) return null;
      let newTopic = prev.topic;
      if (field === "eventDefinition") {
        newTopic = ethers.keccak256(ethers.toUtf8Bytes(value));
      }
      return {
        ...prev,
        [field]: value,
        topic: newTopic,
      };
    });
  };

  const handlePrepareContractChange = (
    index: number,
    field: string,
    value: string
  ) => {
    setEditForm((prev) => {
      if (!prev) return null;
      const newPrepareContracts = [...prev.prepareContracts];
      newPrepareContracts[index] = {
        ...newPrepareContracts[index],
        [field]: value,
      };

      return {
        ...prev,
        prepareContracts: newPrepareContracts,
      };
    });
  };

  const editValidate = async () => {
    console.log("editForm", editForm);
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/put`,
      data: editForm,
    });
    if (response.status === 200) {
      toast.success("Governance fuzzing updated");
      window.location.reload();
    } else {
      toast.error("Fail to update Governance Fuzzing");
    }
  };

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="mx-auto max-w-7xl px-6 py-12">
        <div className="mb-8">
          <H1 className="mb-6 text-accent-primary">Governance Fuzzing</H1>
          <Body3 color="secondary" className="mb-2">
            Setup Event Listeners tied to your contract
          </Body3>
          <Body3 color="secondary">
            Please talk to staff to set these up if you need help
          </Body3>
        </div>

        <div className="mb-8">
          <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)}>
              <h3 className="mb-[22px] text-[28px] leading-[33px] text-fore-neutral-primary">
                Create Governance Fuzzing Setup
              </h3>

              <div className={FORM_STYLES.formContainer}>
                <div className="space-y-6">
                  <div className={FORM_STYLES.inputGroup}>
                    <div className={FORM_STYLES.fieldContainer}>
                      <AppInput
                        className={FORM_STYLES.input}
                        label="Contract Address"
                        {...register("contractAddress", { required: true })}
                        type="text"
                      />
                    </div>
                    <div className={FORM_STYLES.fieldContainer}>
                      <AppInputDropdown
                        className={FORM_STYLES.input}
                        label="Chain"
                        {...register("chainId")}
                        type="text"
                        dropdownItems={chains.map((chain) => ({
                          id: chain.id.toString(),
                          label: `${chain.id} - ${chain.name}`,
                          fields: chain,
                        }))}
                        onItemSelect={(id) => setValue("chainId", Number(id))}
                      />
                    </div>
                  </div>

                  {recipes && recipes.length > 0 && !isLoading ? (
                    <div className={FORM_STYLES.inputGroupSingle}>
                      <div className={FORM_STYLES.fieldContainer}>
                        <AppInputDropdown
                          className={FORM_STYLES.input}
                          {...register("recipeId", { required: true })}
                          type="text"
                          label="Recipe"
                          placeholder="Search by Name, Id, repo ..."
                          dropdownItems={recipes.map((rec) => ({
                            id: rec.id,
                            label: `${rec.displayName}`,
                            fields: rec,
                          }))}
                          onItemSelect={(id) =>
                            setValue("recipeId", id as string)
                          }
                        />
                      </div>
                    </div>
                  ) : isLoading ? (
                    <Body3 color="primary">Loading recipes ...</Body3>
                  ) : (
                    <Body3 color="primary">No recipes found</Body3>
                  )}

                  {contractAddress && chainId && recipeId && (
                    <div className="space-y-6">
                      <div className={FORM_STYLES.divider} />

                      {!showEventDefinition ? (
                        <div className={FORM_STYLES.inputGroupSingle}>
                          <div className={FORM_STYLES.fieldContainer}>
                            <AppButton
                              type="button"
                              onClick={() => setShowEventDefinition(true)}
                            >
                              Define New Event
                            </AppButton>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-6">
                          <h3 className={FORM_STYLES.sectionTitle}>
                            Event Definition
                          </h3>

                          <div className={FORM_STYLES.inputGroupSingle}>
                            <div className={FORM_STYLES.fieldContainer}>
                              <AppInput
                                className={FORM_STYLES.input}
                                label="Event Name"
                                placeholder="e.g. AddNumber"
                                {...register("eventName", { required: true })}
                                type="text"
                              />
                            </div>
                          </div>

                          <div className="space-y-4">
                            <H2 className="mb-[16px]" color="primary">
                              Event Parameters ( MUST match event definition )
                            </H2>

                            {fields.map((field, index) => (
                              <div
                                key={field.id}
                                className="relative mb-[16px] rounded-lg border border-stroke-neutral-decorative p-6"
                              >
                                <div className={FORM_STYLES.inputGroup}>
                                  <div className={FORM_STYLES.fieldContainer}>
                                    <AppInput
                                      className={FORM_STYLES.input}
                                      label="Parameter Type"
                                      placeholder="e.g. address, uint256"
                                      {...register(`parameters.${index}.type`, {
                                        required: true,
                                      })}
                                      type="text"
                                    />
                                  </div>
                                  <div className={FORM_STYLES.fieldContainer}>
                                    <Body3
                                      as="div"
                                      className="flex items-center gap-2 pt-6"
                                      color="secondary"
                                    >
                                      <input
                                        type="checkbox"
                                        {...register(
                                          `parameters.${index}.isIndexed`
                                        )}
                                        className="size-4"
                                      />
                                      Indexed
                                    </Body3>
                                  </div>
                                </div>

                                <div className={FORM_STYLES.inputGroupSingle}>
                                  <div className={FORM_STYLES.fieldContainer}>
                                    <AppInput
                                      className={FORM_STYLES.input}
                                      label="Replacement"
                                      {...register(
                                        `parameters.${index}.replacement`
                                      )}
                                      type="text"
                                      placeholder='bytes constant PAYLOAD = bytes(hex"XX")'
                                    />
                                  </div>
                                </div>

                                <div className="mb-4 flex items-center gap-2">
                                  <input
                                    type="checkbox"
                                    {...register(`parameters.${index}.unused`)}
                                    className="size-4"
                                  />
                                  <Body3 color="secondary">Unused</Body3>
                                </div>

                                <Body3 color="secondary" className="text-sm">
                                  Ex:{" "}
                                  <code className="rounded bg-back-neutral-tertiary px-1">
                                    uint8 DECIMALS = uint8(XX)
                                  </code>
                                  <br />
                                  Define the replacement in your contract. Note
                                  that you need to input XX to allow us to
                                  replace XX with the value from the event
                                </Body3>

                                <AppButton
                                  type="button"
                                  onClick={() => remove(index)}
                                  className="absolute right-2 top-2 text-status-error"
                                  variant="secondary"
                                  size="sm"
                                >
                                  Remove
                                </AppButton>
                              </div>
                            ))}

                            <AppButton
                              type="button"
                              onClick={() =>
                                append({
                                  type: "",
                                  isIndexed: false,
                                  replacement: "",
                                  unused: false,
                                })
                              }
                            >
                              Add Parameter
                            </AppButton>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              {topic && eventDefinition && (
                <div className="space-y-4">
                  <div className={FORM_STYLES.divider} />

                  <div className="space-y-2">
                    <h3 className={FORM_STYLES.sectionTitle}>
                      Generated Event Information
                    </h3>
                    <div className="space-y-2 rounded-lg bg-back-neutral-tertiary p-4">
                      <Body3 color="primary">
                        <strong>Topic:</strong> {topic}
                      </Body3>
                      <Body3 color="primary">
                        <strong>Event Definition:</strong> {eventDefinition}
                      </Body3>
                    </div>
                    <Body3 color="secondary" className="text-sm">
                      Make sure you verify this topic is correct before
                      submitting. Incorrect topic will make us miss events on
                      chain.
                    </Body3>
                  </div>
                </div>
              )}

              {showEventDefinition && (
                <div className="mt-8">
                  <AppButton type="submit">
                    Submit Governance Fuzzing Setup
                  </AppButton>
                </div>
              )}
            </form>
          </FormProvider>
        </div>

        {governanceFuzzing?.length > 0 && (
          <div className="space-y-6">
            <H2 className="text-accent-primary">
              Existing Governance Fuzzing Setups
            </H2>

            <div className="space-y-6">
              {governanceFuzzing.map((govFuzz) => (
                <div
                  key={govFuzz.id}
                  className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary p-6 shadow-sm"
                >
                  {isEditing.get(govFuzz.id) && editForm ? (
                    <div className="space-y-6">
                      <h3 className={FORM_STYLES.sectionTitle}>
                        Edit Governance Fuzzing Setup
                      </h3>

                      <div className={FORM_STYLES.inputGroup}>
                        <div className={FORM_STYLES.fieldContainer}>
                          <AppInput
                            className={FORM_STYLES.input}
                            label="Address"
                            type="text"
                            value={editForm.address}
                            onChange={(e) =>
                              handleInputChange("address", e.target.value)
                            }
                          />
                        </div>
                        <div className={FORM_STYLES.fieldContainer}>
                          <AppInput
                            className={FORM_STYLES.input}
                            label="Chain Id"
                            type="text"
                            value={editForm.chainId}
                            onChange={(e) =>
                              handleInputChange("chainId", e.target.value)
                            }
                          />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <Body3 color="primary" className="font-semibold">
                          Prepare Contracts
                        </Body3>
                        <div className="space-y-3">
                          {editForm.prepareContracts.map((contract, index) => (
                            <AppInput
                              key={`contract-${contract.target}-${index}`}
                              className={FORM_STYLES.input}
                              type="text"
                              value={contract.target}
                              placeholder="Contract address"
                              onChange={(e) =>
                                handlePrepareContractChange(
                                  index,
                                  "target",
                                  e.target.value
                                )
                              }
                            />
                          ))}
                        </div>
                      </div>

                      <div className={FORM_STYLES.inputGroupSingle}>
                        <div className={FORM_STYLES.fieldContainer}>
                          <AppInput
                            className={FORM_STYLES.input}
                            label="Event Definition"
                            type="text"
                            value={editForm.eventDefinition}
                            onChange={(e) =>
                              handleInputChange(
                                "eventDefinition",
                                e.target.value
                              )
                            }
                          />
                        </div>
                      </div>

                      <div className={FORM_STYLES.inputGroupSingle}>
                        <div className={FORM_STYLES.fieldContainer}>
                          <AppInput
                            className={FORM_STYLES.input}
                            label="Topic"
                            type="text"
                            value={editForm.topic}
                            onChange={() =>
                              handleInputChange(
                                "topic",
                                ethers.keccak256(
                                  ethers.toUtf8Bytes(editForm.eventDefinition)
                                )
                              )
                            }
                            disabled
                          />
                        </div>
                      </div>

                      <div className="flex gap-4">
                        <AppButton type="button" onClick={editValidate}>
                          Validate Edit
                        </AppButton>
                        <AppButton
                          type="button"
                          variant="secondary"
                          onClick={() =>
                            setIsEditing(
                              new Map(isEditing).set(govFuzz.id, false)
                            )
                          }
                        >
                          Cancel Edit
                        </AppButton>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-fore-neutral-primary">
                          Contract: {govFuzz.address}
                        </h3>
                        <div className="flex gap-3">
                          <AppButton
                            type="button"
                            size="sm"
                            variant="secondary"
                            onClick={() => handleEdit(govFuzz.id)}
                          >
                            Edit
                          </AppButton>
                          <AppButton
                            type="button"
                            size="sm"
                            onClick={() => handleToggle(govFuzz.id)}
                          >
                            Toggle
                          </AppButton>
                          <AppButton
                            type="button"
                            size="sm"
                            variant="secondary"
                            onClick={() => handleDelete(govFuzz.id)}
                            className="text-status-error"
                          >
                            Delete
                          </AppButton>
                        </div>
                      </div>

                      <AppCode
                        code={JSON.stringify(govFuzz, null, 2)}
                        language="json"
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
